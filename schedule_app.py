#!/usr/bin/env python3
"""
Schedule Processing App - Excel Template Output
Saves results to Excel file in the same format as W2TE1MAN2510_TEQ_GREEN_POWER_XI_RTM.xlsm
"""

import streamlit as st
import pandas as pd
import openpyxl
from io import BytesIO
import shutil
from datetime import datetime, timedelta
import pytz
import time
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email.mime.text import MIMEText
from email import encoders

def create_output_excel(teq_g_values, o2_values, output_type="TEQ G"):
    """Create Excel file in the same format as the template"""

    # Get current IST time and determine the correct date
    ist = pytz.timezone('Asia/Kolkata')
    current_time = datetime.now(ist)

    # If time is after 10:15 PM (22:15), use next day's date
    if current_time.hour >= 22 and current_time.minute >= 15:
        from datetime import timedelta
        delivery_date = current_time.date() + timedelta(days=1)
    else:
        delivery_date = current_time.date()

    # Choose the appropriate template and values
    if output_type == "TEQ G":
        template_file = "W2TE1MAN2510_TEQ_GREEN_POWER_XI_RTM.xlsm"
        values_to_use = teq_g_values
        output_filename = f"W2TE1MAN2510_TEQ_GREEN_POWER_XI_RTM_{delivery_date.strftime('%d%m%Y')}.xlsm"
    else:
        template_file = "W2OR1MAN2511_O2RE3PL_Wasi_klm_W_RTM.xlsm"
        values_to_use = o2_values
        output_filename = f"W2OR1MAN2511_O2RE3PL_Wasi_klm_W_RTM_{delivery_date.strftime('%d%m%Y')}.xlsm"

    # Load the template workbook (keep_vba=True to preserve macros)
    wb = openpyxl.load_workbook(template_file, keep_vba=True)
    ws = wb.active  # Get the first sheet

    # Update the delivery date in cell D12
    ws['D12'] = delivery_date

    # Write values to J21:J116 (96 rows)
    for i, value in enumerate(values_to_use):
        row_num = 21 + i  # Start from row 21
        ws[f'J{row_num}'] = value

    # Save to BytesIO with .xlsm format (preserve macros)
    output = BytesIO()
    wb.save(output)
    output.seek(0)

    return output, output_filename

def send_email_with_attachment(excel_data, filename, subject, portfolio_info):
    """Send email with Excel attachment"""
    # Email configuration
    SMTP_SERVER = "mx1.50hertz.in"
    SMTP_PORT = 25
    SENDER_EMAIL = "<EMAIL>"

    # Email recipients
    TO_EMAILS = ["<EMAIL>", "<EMAIL>"]
    CC_EMAILS = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ]
    BCC_EMAILS = ["<EMAIL>"]

    try:
        # Create message
        msg = MIMEMultipart()
        msg['From'] = SENDER_EMAIL
        msg['To'] = ", ".join(TO_EMAILS)
        msg['Cc'] = ", ".join(CC_EMAILS)
        msg['Bcc'] = ", ".join(BCC_EMAILS)
        msg['Subject'] = subject

        # Email body with portfolio information and professional signature
        body = f"""Dear Team,

Please find attached the RTM bid file for the scheduled date.

Portfolio: {portfolio_info['name']}
Portfolio Number: {portfolio_info['number']}
File: {filename}
Generated on: {datetime.now(pytz.timezone('Asia/Kolkata')).strftime('%d-%m-%Y %H:%M:%S IST')}

Warm Regards,

Shift In-charge

Manikaran Analytics Limited | 3rd FL, D-21|Corporate Park, Sec-21| Dwarka | New Delhi -110075 |
M: +91-9163536666 | T: +91 1165651994 | F: +91 1145768467 |
E: <EMAIL> |W: www.manikarananalytics.in |"""

        msg.attach(MIMEText(body, 'plain'))

        # Attach Excel file (.xlsm format)
        attachment = MIMEBase('application', 'vnd.ms-excel.sheet.macroEnabled.12')
        attachment.set_payload(excel_data.getvalue())
        encoders.encode_base64(attachment)
        attachment.add_header(
            'Content-Disposition',
            f'attachment; filename="{filename}"'
        )
        msg.attach(attachment)

        # Send email to all recipients (To + Cc + Bcc)
        all_recipients = TO_EMAILS + CC_EMAILS + BCC_EMAILS
        server = smtplib.SMTP(SMTP_SERVER, SMTP_PORT)
        server.sendmail(SENDER_EMAIL, all_recipients, msg.as_string())
        server.quit()

        return True, "Email sent successfully"

    except Exception as e:
        return False, f"Failed to send email: {str(e)}"

def auto_calculate_results(schedule_values):
    """Automatically calculate results when 96 rows are entered"""
    # Process all 96 values
    results = []
    teq_g_values = []
    o2_values = []

    for i, original_value in enumerate(schedule_values):
        if original_value > 0:
            # Step 2: If original < 7, make it 7, then subtract 7
            adjusted_original = max(7, original_value)
            result = adjusted_original - 7

            # Calculate TEQ G and O2
            teq_g = (result / 44.3) * 27
            o2 = (result / 44.3) * 17.3
            verified = abs((teq_g + o2) - result) < 0.0001
        else:
            adjusted_original = 0
            result = 0
            teq_g = 0
            o2 = 0
            verified = True

        teq_g_values.append(round(teq_g, 1))
        o2_values.append(round(o2, 1))

        results.append({
            'Block': i + 1,
            'Original': adjusted_original,
            'Result': result,
            'TEQ G': round(teq_g, 1),
            'O2': round(o2, 1),
            'Verified': verified
        })

    # Display results for non-zero values only
    non_zero_results = [r for r in results if r['Original'] > 0]

    if non_zero_results:
        st.subheader("📊 Auto-Generated Results")
        results_df = pd.DataFrame(non_zero_results)
        st.dataframe(results_df, use_container_width=True)

        # Summary
        st.write(f"Processed Blocks: {len(non_zero_results)}")
        st.write(f"Total TEQ G: {sum(r['TEQ G'] for r in non_zero_results):.1f}")
        st.write(f"Total O2: {sum(r['O2'] for r in non_zero_results):.1f}")
        st.write(f"All Verified: {all(r['Verified'] for r in non_zero_results)}")

        # Auto-send emails
        st.subheader("� Sending Email Notifications")

        # Get delivery date for email subject
        ist = pytz.timezone('Asia/Kolkata')
        current_time = datetime.now(ist)

        if current_time.hour >= 22 and current_time.minute >= 15:
            delivery_date = current_time.date() + timedelta(days=1)
        else:
            delivery_date = current_time.date()

        date_str = delivery_date.strftime('%d-%m-%Y')

        # Send TEQ G email
        st.write("📤 Sending TEQ G file...")
        try:
            teq_g_excel, teq_g_filename = create_output_excel(teq_g_values, o2_values, "TEQ G")
            teq_g_subject = f"RTM BID of TEQ GREEN POWER XI PRIVATE LTD. for date {date_str} (PF:- W2TE1MAN2510)"
            teq_g_portfolio = {
                'name': 'TEQ GREEN POWER XI PRIVATE LTD.',
                'number': 'W2TE1MAN2510'
            }

            success, message = send_email_with_attachment(teq_g_excel, teq_g_filename, teq_g_subject, teq_g_portfolio)

            if success:
                st.success("✅ TEQ G email sent successfully to all recipients")
            else:
                st.error(f"❌ TEQ G email failed: {message}")

        except Exception as e:
            st.error(f"❌ Error creating TEQ G file: {e}")

        # Send O2 email
        st.write("📤 Sending O2 file...")
        try:
            o2_excel, o2_filename = create_output_excel(teq_g_values, o2_values, "O2")
            o2_subject = f"RTM BID of O2 RENEWABLE ENERGY III PRIVATE LTD. for date {date_str} (PF:- W2OR1MAN2511)"
            o2_portfolio = {
                'name': 'O2 RENEWABLE ENERGY III PRIVATE LTD.',
                'number': 'W2OR1MAN2511'
            }

            success, message = send_email_with_attachment(o2_excel, o2_filename, o2_subject, o2_portfolio)

            if success:
                st.success("✅ O2 email sent successfully to all recipients")
            else:
                st.error(f"❌ O2 email failed: {message}")

        except Exception as e:
            st.error(f"❌ Error creating O2 file: {e}")

        st.info("📧 Both emails sent to multiple recipients (To, Cc, Bcc)")
        st.info(f"� Delivery date used in subject: {date_str}")

@st.fragment(run_every=1)  # Update every 1 second
def display_live_clock():
    """Display live updating clock"""
    ist = pytz.timezone('Asia/Kolkata')
    current_time = datetime.now(ist)
    st.info(f"📅 Current Date & Time (IST): {current_time.strftime('%d-%m-%Y %H:%M:%S')}")

def main():
    """Main application"""
    st.title("Schedule Processing - Excel Template Output")

    # Important warning message in red
    st.markdown(
        """
        <div style="background-color: #ffebee; border: 2px solid #f44336; border-radius: 5px; padding: 10px; margin: 10px 0;">
            <p style="color: #d32f2f; font-weight: bold; font-size: 16px; margin: 0; text-align: center;">
                ⚠️ USING TOOL AFTER 22:15 IS FOR NEXT DAYS RTM ⚠️
            </p>
        </div>
        """,
        unsafe_allow_html=True
    )

    # Display auto-updating clock
    display_live_clock()

    # Show which date will be used for Excel output
    ist = pytz.timezone('Asia/Kolkata')
    current_time = datetime.now(ist)

    if current_time.hour >= 22 and current_time.minute >= 15:
        from datetime import timedelta
        delivery_date = current_time.date() + timedelta(days=1)
        st.success(f"📅 Excel Output Date: {delivery_date.strftime('%d-%m-%Y')} (Next Day - Time after 10:15 PM)")
    else:
        delivery_date = current_time.date()
        st.success(f"📅 Excel Output Date: {delivery_date.strftime('%d-%m-%Y')} (Current Day)")

    st.write("Enter schedule values and save results to Excel template format")
    
    # Initialize data
    if 'data' not in st.session_state:
        st.session_state.data = pd.DataFrame({
            'Block': range(1, 97),
            'Schedule Value': [0.0] * 96
        })
    
    # Data input
    st.subheader("Input Schedule Values (96 Blocks)")
    st.write("Enter one value per line (96 lines total). You can copy and paste from Excel column.")

    # Initialize text input from session state
    if 'text_input' not in st.session_state:
        st.session_state.text_input = ""

    # Text area for input
    text_input = st.text_area(
        "Schedule Values (one per line):",
        value=st.session_state.text_input,
        height=300,
        placeholder="Enter schedule values, one per line:\n50.5\n75.2\n12.8\n..."
    )

    st.session_state.text_input = text_input

    # Parse the text input
    lines = text_input.strip().split('\n') if text_input.strip() else []
    schedule_values = []

    for i in range(96):
        if i < len(lines) and lines[i].strip():
            try:
                value = float(lines[i].strip())
                schedule_values.append(value)
            except ValueError:
                schedule_values.append(0.0)
        else:
            schedule_values.append(0.0)

    # Update session state data
    st.session_state.data = pd.DataFrame({
        'Block': range(1, 97),
        'Schedule Value': schedule_values
    })

    # Show preview of parsed data
    non_zero_count = sum(1 for v in schedule_values if v > 0)
    total_lines = len([line for line in lines if line.strip()])

    st.write(f"Parsed {non_zero_count} non-zero values | Total lines entered: {total_lines}/96")

    if non_zero_count > 0:
        preview_data = [(i+1, v) for i, v in enumerate(schedule_values) if v > 0]
        st.write("Preview of non-zero values:")
        preview_df = pd.DataFrame(preview_data[:10], columns=['Block', 'Value'])
        st.dataframe(preview_df, use_container_width=True)

    # Auto-calculate when 96 rows of data are entered
    if total_lines >= 96:
        st.success("✅ 96 rows detected - Auto-calculating results...")
        auto_calculate_results(schedule_values)
    elif total_lines > 0:
        st.info(f"📝 Enter {96 - total_lines} more lines to auto-calculate results")
    

    


if __name__ == "__main__":
    main()
